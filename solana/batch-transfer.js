// solTransfers.js
import fs from "fs";
import bs58 from "bs58";
import BN from "bn.js";
import {
  Connection,
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  PublicKey,
} from "@solana/web3.js";
import {
  getOrCreateAssociatedTokenAccount,
  createTransferCheckedInstruction,
  getAccount,
} from "@solana/spl-token";
import * as anchor from "@project-serum/anchor";

const STAKING_IDL = {
  version: "0.1.0",
  name: "token_staking_program",
  instructions: [
    {
      name: "claim_to_stake",
      discriminator: [196, 151, 171, 133, 183, 192, 205, 198],
      accounts: [
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim_status",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117, 115],
              },
              {
                kind: "account",
                path: "pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "********************************",
        },
      ],
      args: [
        {
          name: "allocation",
          type: "u64",
        },
        {
          name: "proof",
          type: {
            vec: {
              array: ["u8", 32],
            },
          },
        },
      ],
    },
  ],
};

const MAIN_WALLET = "DkjL9fNcNXWfu1G1xDsctrn6WXXb3epyphKpyQurAhaU";
const LEAF_ADDRESS = "EWbYEzhuyNm8pZntv1bbHUQtsJCW1esErofEUSyYpump";
const DECIMALS = 6;
const PROGRAM_ID = new PublicKey(
  "EsybJRDoqxAnnBBwjvQ42X1xkgokZEKmSP9Y8sZV7bzN"
);
const connection = new Connection(clusterApiUrl("mainnet-beta"), "confirmed");

export function loadKeypair(pathOrBase58) {
  if (fs.existsSync(pathOrBase58)) {
    const raw = JSON.parse(fs.readFileSync(pathOrBase58, "utf8"));
    return Keypair.fromSecretKey(Uint8Array.from(raw));
  }
  try {
    const secret = bs58.decode(pathOrBase58);
    return Keypair.fromSecretKey(secret);
  } catch (e) {
    throw new Error(
      "Invalid keypair input. Provide path to JSON or base58 secret key."
    );
  }
}

export async function transferSol(fromKeypair, toPubkey) {
  const toPub =
    typeof toPubkey === "string" ? new PublicKey(toPubkey) : toPubkey;
  const lamports = getSolBalance(fromKeypair.publicKey);

  console.log(`transferring sol from ${fromKeypair.publicKey} to ${toPub}`);

  const tx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports,
    })
  );

  const { blockhash } = await connection.getLatestBlockhash("confirmed");
  tx.recentBlockhash = blockhash;

  const estimatedFee = await connection.getFeeForMessage(
    tx.compileMessage(),
    "confirmed"
  );

  const finaltx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports: lamports - estimatedFee.value,
    })
  );

  const sig = await sendAndConfirmTransaction(connection, finaltx, [
    fromKeypair,
  ]);

  console.log(`Transfer sol success from ${fromKeypair.publicKey} to ${toPub}`);
  return sig; // transaction signature
}

export async function transferSPLToken(fromKeypair) {
  const fromPubkey = fromKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);
  const destPubkey = new PublicKey(MAIN_WALLET);

  const { amount, tokenAccount } = await getSPLTokenBalance(fromKeypair);
  const destTokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    fromKeypair,
    mintPubkey,
    destPubkey
  );

  const ix = createTransferCheckedInstruction(
    tokenAccount.address, // Source token account
    mintPubkey, // Mint
    destTokenAccount.address, // Destination token account
    fromPubkey, // Owner of source token account
    amount, // Amount in smallest unit
    DECIMALS // Decimals
  );

  const tx = new Transaction().add(ix);
  const sig = await sendAndConfirmTransaction(connection, tx, [fromKeypair]);
  console.log(`transfer token of ${amount} from ${fromPubkey}  success`);
  return sig;
}

export async function getSolBalance(publicKey) {
  try {
    const pubKey = new PublicKey(publicKey);
    const balanceLamports = await connection.getBalance(pubKey);
    return balanceLamports;
  } catch (error) {
    console.error("Error fetching SOL balance:", error);
    throw error;
  }
}

export async function getSPLTokenBalance(walletKeypair) {
  const walletPubkey = walletKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);

  const tokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    walletKeypair,
    mintPubkey,
    walletPubkey
  );
  const accountInfo = await getAccount(connection, tokenAccount.address);

  console.log(`Balance of ${walletPubkey} is ${accountInfo.amount}`);

  return { amount: accountInfo.amount, tokenAccount };
}

const chunkArray = () => {
  const chunkSize = 10;
  const chunks = [];
  for (let i = 0; i < PRIVATE_KEYS.length; i += chunkSize) {
    chunks.push(PRIVATE_KEYS.slice(i, i + chunkSize));
  }
  return chunks;
};

const handlerTransferBatch = async () => {
  const chunks = chunkArray();
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const nextChunk = chunks[i + 1];

    const promises = chunk.map(async (privateKey, index) => {
      const keypair = loadKeypair(privateKey);
      const nextAccountKeypair = loadKeypair(nextChunk[index]);

      await transferSPLToken(keypair);
      await transferSol(keypair, nextAccountKeypair.publicKey);
    });
    await Promise.settle(promises);
  }
};

// const handlerTransferSol = async () => {
//   for (let i = 0; i < 1000; i++) {
//     const currentAccountKeypair = loadKeypair(PRIVATE_KEYS[i]);
//     const nextAccountKeypair = loadKeypair(PRIVATE_KEYS[i + 1]);

//     await transferSPLToken(currentAccountKeypair);
//     await transferSol(currentAccountKeypair, nextAccountKeypair.publicKey);
//   }

//   transferSPLToken(account);
// };

// handlerTransferBatch();

async function completeUnstake(walletKeypair, args) {
  const provider = new anchor.AnchorProvider(
    connection,
    new anchor.Wallet(walletKeypair),
    { preflightCommitment: "confirmed" }
  );

  const program = new anchor.Program(STAKING_IDL, PROGRAM_ID, provider);

  // Derive user_stake PDA
  const [userStake] = PublicKey.findProgramAddressSync(
    [
      Buffer.from("user_stake"),
      new PublicKey(args.stakingPool).toBuffer(),
      walletKeypair.publicKey.toBuffer(),
    ],
    PROGRAM_ID
  );

  // Get associated token accounts
  const userTokenAccount = await anchor.utils.token.associatedAddress({
    mint: new PublicKey(args.stakingMint),
    owner: walletKeypair.publicKey,
  });

  const userRewardAccount = await anchor.utils.token.associatedAddress({
    mint: new PublicKey(args.rewardMint),
    owner: walletKeypair.publicKey,
  });

  const tx = await program.methods
    .completeUnstake()
    .accounts({
      user: walletKeypair.publicKey,
      stakingPool: new PublicKey(args.stakingPool),
      userStake: userStake,
      stakingVault: new PublicKey(args.stakingVault),
      userTokenAccount: userTokenAccount,
      rewardVault: new PublicKey(args.rewardVault),
      userRewardAccount: userRewardAccount,
      stakingMint: new PublicKey(args.stakingMint),
      rewardMint: new PublicKey(args.rewardMint),
      tokenProgram: new PublicKey(
        "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
      ),
      associatedTokenProgram: new PublicKey(
        "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
      ),
      systemProgram: SystemProgram.programId,
    })
    .signers([walletKeypair])
    .rpc();

  console.log(
    `✅ Complete Unstake TX success for ${walletKeypair.publicKey.toBase58()}`
  );
  console.log(`🔗 https://solscan.io/tx/${tx}`);
  return tx;
}

// Export the complete unstake function
export { completeUnstake };
