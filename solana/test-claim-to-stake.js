// Test script for claim_to_stake function
import { claimToStake, fetchClaimData, loadKeypair } from './batch-transfer.js';

async function testClaimToStake() {
  try {
    console.log("🚀 Starting claim_to_stake test...");
    
    // Replace with your actual private key or path to keypair file
    const PRIVATE_KEY = "your-private-key-here";
    
    // Replace with actual public keys from your program
    const POOL_KEY = "POOL_PUBLIC_KEY_HERE";
    const VAULT_KEY = "VAULT_PUBLIC_KEY_HERE";
    const STAKING_POOL_KEY = "STAKING_POOL_KEY_HERE";
    const STAKING_VAULT_KEY = "STAKING_VAULT_KEY_HERE";
    
    // Load wallet
    console.log("📝 Loading wallet...");
    const walletKeypair = loadKeypair(PRIVATE_KEY);
    console.log(`✅ Wallet loaded: ${walletKeypair.publicKey.toBase58()}`);
    
    // Fetch claim data from API
    console.log("🔍 Fetching claim data from API...");
    const { proof, allocation } = await fetchClaimData();
    console.log(`✅ Allocation: ${allocation}`);
    console.log(`✅ Proof length: ${proof.length}`);
    
    // Prepare arguments
    const claimArgs = {
      pool: POOL_KEY,
      vault: VAULT_KEY,
      stakingPool: STAKING_POOL_KEY,
      stakingVault: STAKING_VAULT_KEY,
      allocation: allocation,
      proof: proof
    };
    
    // Execute claim_to_stake
    console.log("⚡ Executing claim_to_stake transaction...");
    const txSignature = await claimToStake(walletKeypair, claimArgs);
    
    console.log("🎉 Success! Transaction completed:");
    console.log(`📋 Transaction signature: ${txSignature}`);
    console.log(`🔗 View on Solscan: https://solscan.io/tx/${txSignature}`);
    
  } catch (error) {
    console.error("❌ Error during claim_to_stake test:", error);
    
    // Provide helpful error messages
    if (error.message.includes("Invalid keypair")) {
      console.log("💡 Tip: Make sure to replace 'your-private-key-here' with your actual private key or path to keypair file");
    }
    if (error.message.includes("PUBLIC_KEY_HERE")) {
      console.log("💡 Tip: Make sure to replace the placeholder public keys with actual program addresses");
    }
    if (error.message.includes("fetch")) {
      console.log("💡 Tip: Make sure you have access to the claim API and are authenticated");
    }
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testClaimToStake();
}

export { testClaimToStake };
