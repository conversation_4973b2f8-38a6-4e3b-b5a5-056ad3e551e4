// const { BackWood<PERSON><PERSON> } = require("./BackWoodApi");
const fs = require("fs");
const bs58 = require("bs58");
const BN = require("bn.js");
const {
  Connection,
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  PublicKey,
} = require("@solana/web3.js");
const {
  getOrCreateAssociatedTokenAccount,
  createTransferCheckedInstruction,
  getAccount,
} = require("@solana/spl-token");
const anchor = require("@project-serum/anchor");

const connection = new Connection(clusterApiUrl("mainnet-beta"), "confirmed");

const PROGRAM_ID = new PublicKey(
  "EsybJRDoqxAnnBBwjvQ42X1xkgokZEKmSP9Y8sZV7bzN"
);
const STAKING_IDL = {
  version: "0.1.0",
  name: "token_staking_program",
  instructions: [
    {
      name: "add_claimant",
      discriminator: [219, 251, 213, 252, 211, 243, 208, 238],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claimant",
        },
        {
          name: "claim",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109],
              },
              {
                kind: "account",
                path: "pool",
                type: "publicKey",
              },
              {
                kind: "account",
                path: "claimant",
                type: "publicKey",
              },
            ],
          },
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "claim",
      discriminator: [62, 198, 214, 193, 213, 159, 108, 210],
      accounts: [
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109],
              },
              {
                kind: "account",
                path: "pool",
                type: "publicKey",
              },
              {
                kind: "account",
                path: "claimant",
                type: "publicKey",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "claimant_ata",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "claimant",
                type: "publicKey",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "mint",
                type: "publicKey",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [],
    },
    {
      name: "claim_staking_rewards",
      discriminator: [229, 141, 170, 69, 111, 94, 6, 72],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
                type: "publicKey",
              },
              {
                kind: "account",
                path: "user",
                type: "publicKey",
              },
            ],
          },
        },
        {
          name: "reward_vault",
          writable: !0,
        },
        {
          name: "user_reward_account",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "user",
                type: "publicKey",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "reward_mint",
                type: "publicKey",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "reward_mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [],
    },
    {
      name: "claim_to_stake",
      discriminator: [196, 151, 171, 133, 183, 192, 205, 198],
      accounts: [
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim_status",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117, 115],
              },
              {
                kind: "account",
                path: "pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "claimant",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "allocation",
          type: "u64",
        },
        {
          name: "proof",
          type: {
            vec: {
              array: ["u8", 32],
            },
          },
        },
      ],
    },
    {
      name: "claim_with_proof",
      discriminator: [38, 165, 237, 119, 50, 165, 25, 163],
      accounts: [
        {
          name: "claimant",
          writable: !0,
          signer: !0,
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "claim_status",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [99, 108, 97, 105, 109, 95, 115, 116, 97, 116, 117, 115],
              },
              {
                kind: "account",
                path: "pool",
                type: "publicKey",
              },
              {
                kind: "account",
                path: "claimant",
                type: "publicKey",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "claimant_ata",
          writable: !0,
        },
        {
          name: "mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "allocation",
          type: "u64",
        },
        {
          name: "proof",
          type: {
            vec: {
              array: ["u8", 32],
            },
          },
        },
      ],
    },
    {
      name: "close_pool",
      discriminator: [140, 189, 209, 23, 239, 62, 239, 11],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "rent_collector",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [],
    },
    {
      name: "complete_unstake",
      discriminator: [79, 98, 40, 241, 100, 30, 25, 234],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "user_token_account",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "user",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "staking_mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "reward_vault",
          writable: !0,
        },
        {
          name: "user_reward_account",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "account",
                path: "user",
              },
              {
                kind: "const",
                value: [
                  6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206,
                  235, 121, 172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140,
                  245, 133, 126, 255, 0, 169,
                ],
              },
              {
                kind: "account",
                path: "reward_mint",
              },
            ],
            program: {
              kind: "const",
              value: [
                140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142,
                13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216,
                219, 233, 248, 89,
              ],
            },
          },
        },
        {
          name: "staking_mint",
        },
        {
          name: "reward_mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [],
    },
    {
      name: "freeze_pool",
      discriminator: [211, 216, 1, 216, 54, 191, 102, 150],
      accounts: [
        {
          name: "authority",
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
      ],
      args: [],
    },
    {
      name: "fund_pool",
      discriminator: [36, 57, 233, 176, 181, 20, 87, 159],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "authority_token_account",
          writable: !0,
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "fund_rewards",
      discriminator: [114, 64, 163, 112, 175, 167, 19, 121],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "reward_vault",
          writable: !0,
        },
        {
          name: "authority_token_account",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "initialize_pool",
      discriminator: [95, 180, 10, 172, 84, 174, 232, 40],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
        },
        {
          name: "mint",
        },
        {
          name: "pool",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [112, 111, 111, 108],
              },
              {
                kind: "account",
                path: "mint",
              },
              {
                kind: "account",
                path: "authority",
              },
            ],
          },
        },
        {
          name: "vault",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [118, 97, 117, 108, 116],
              },
              {
                kind: "account",
                path: "pool",
              },
            ],
          },
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "args",
          type: {
            defined: "InitializePoolArgs",
          },
        },
      ],
    },
    {
      name: "initialize_staking_pool",
      discriminator: [231, 155, 216, 76, 185, 211, 34, 151],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
        },
        {
          name: "mint",
        },
        {
          name: "reward_mint",
        },
        {
          name: "staking_pool",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [
                  115, 116, 97, 107, 105, 110, 103, 95, 112, 111, 111, 108,
                ],
              },
              {
                kind: "account",
                path: "mint",
              },
              {
                kind: "account",
                path: "authority",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [
                  115, 116, 97, 107, 105, 110, 103, 95, 118, 97, 117, 108, 116,
                ],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
            ],
          },
        },
        {
          name: "reward_vault",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [
                  114, 101, 119, 97, 114, 100, 95, 118, 97, 117, 108, 116,
                ],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
            ],
          },
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "args",
          type: {
            defined: "InitializeStakingPoolArgs",
          },
        },
      ],
    },
    {
      name: "request_unlock",
      discriminator: [114, 219, 84, 115, 190, 220, 130, 240],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
      ],
      args: [],
    },
    {
      name: "set_merkle_root",
      discriminator: [43, 24, 91, 60, 240, 137, 28, 102],
      accounts: [
        {
          name: "authority",
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
      ],
      args: [
        {
          name: "merkle_root",
          type: {
            array: ["u8", 32],
          },
        },
        {
          name: "enable",
          type: "bool",
        },
      ],
    },
    {
      name: "stake_tokens",
      discriminator: [136, 126, 91, 162, 40, 131, 13, 127],
      accounts: [
        {
          name: "user",
          writable: !0,
          signer: !0,
        },
        {
          name: "staking_pool",
          writable: !0,
        },
        {
          name: "user_stake",
          writable: !0,
          pda: {
            seeds: [
              {
                kind: "const",
                value: [117, 115, 101, 114, 95, 115, 116, 97, 107, 101],
              },
              {
                kind: "account",
                path: "staking_pool",
              },
              {
                kind: "account",
                path: "user",
              },
            ],
          },
        },
        {
          name: "staking_vault",
          writable: !0,
        },
        {
          name: "user_token_account",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
    {
      name: "unfreeze_pool",
      discriminator: [236, 22, 34, 179, 44, 68, 15, 108],
      accounts: [
        {
          name: "authority",
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
      ],
      args: [],
    },
    {
      name: "withdraw_unallocated",
      discriminator: [226, 26, 221, 64, 218, 61, 68, 231],
      accounts: [
        {
          name: "authority",
          writable: !0,
          signer: !0,
          relations: ["pool"],
        },
        {
          name: "pool",
          writable: !0,
        },
        {
          name: "vault",
          writable: !0,
        },
        {
          name: "authority_token_account",
          writable: !0,
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
      ],
      args: [
        {
          name: "amount",
          type: "u64",
        },
      ],
    },
  ],
  accounts: [
    {
      name: "Claim",
      type: {
        kind: "struct",
        fields: [
          {
            name: "pool",
            type: "publicKey",
          },
          {
            name: "claimant",
            type: "publicKey",
          },
          {
            name: "total_allocation",
            type: "u64",
          },
          {
            name: "claimed_amount",
            type: "u64",
          },
          {
            name: "bump",
            type: "u8",
          },
        ],
      },
    },
    {
      name: "ClaimStatus",
      type: {
        kind: "struct",
        fields: [
          {
            name: "pool",
            type: "publicKey",
          },
          {
            name: "claimant",
            type: "publicKey",
          },
          {
            name: "claimed_amount",
            type: "u64",
          },
          {
            name: "bump",
            type: "u8",
          },
        ],
      },
    },
    {
      name: "Pool",
      type: {
        kind: "struct",
        fields: [
          {
            name: "authority",
            type: "publicKey",
          },
          {
            name: "mint",
            type: "publicKey",
          },
          {
            name: "vault",
            type: "publicKey",
          },
          {
            name: "bump",
            type: "u8",
          },
          {
            name: "is_frozen",
            type: "bool",
          },
          {
            name: "total_deposited",
            type: "u64",
          },
          {
            name: "total_claimed",
            type: "u64",
          },
          {
            name: "start_ts",
            type: "i64",
          },
          {
            name: "end_ts",
            type: "i64",
          },
          {
            name: "merkle_root",
            type: {
              array: ["u8", 32],
            },
          },
          {
            name: "merkle_enabled",
            type: "bool",
          },
        ],
      },
    },
    {
      name: "StakingPool",
      type: {
        kind: "struct",
        fields: [
          {
            name: "authority",
            type: "publicKey",
          },
          {
            name: "mint",
            type: "publicKey",
          },
          {
            name: "reward_mint",
            type: "publicKey",
          },
          {
            name: "staking_vault",
            type: "publicKey",
          },
          {
            name: "reward_vault",
            type: "publicKey",
          },
          {
            name: "bump",
            type: "u8",
          },
          {
            name: "is_active",
            type: "bool",
          },
          {
            name: "total_staked",
            type: "u64",
          },
          {
            name: "total_rewards_distributed",
            type: "u64",
          },
          {
            name: "reward_rate",
            type: "u64",
          },
          {
            name: "start_time",
            type: "i64",
          },
          {
            name: "end_time",
            type: "i64",
          },
          {
            name: "unlock_duration",
            type: "i64",
          },
        ],
      },
    },
    {
      name: "UserStake",
      type: {
        kind: "struct",
        fields: [
          {
            name: "staking_pool",
            type: "publicKey",
          },
          {
            name: "user",
            type: "publicKey",
          },
          {
            name: "staked_amount",
            type: "u64",
          },
          {
            name: "staked_at",
            type: "i64",
          },
          {
            name: "last_claim_ts",
            type: "i64",
          },
          {
            name: "pending_rewards",
            type: "u64",
          },
          {
            name: "unlock_requested_at",
            type: "i64",
          },
          {
            name: "bump",
            type: "u8",
          },
        ],
      },
    },
  ],
  errors: [
    {
      code: 6e3,
      name: "InvalidAmount",
      msg: "Invalid amount",
    },
    {
      code: 6001,
      name: "Overflow",
      msg: "Arithmetic overflow",
    },
    {
      code: 6002,
      name: "ExceedsUnclaimed",
      msg: "Withdraw amount exceeds unclaimed balance",
    },
  ],
  types: [
    {
      name: "InitializePoolArgs",
      type: {
        kind: "struct",
        fields: [
          {
            name: "start_ts",
            type: "i64",
          },
          {
            name: "end_ts",
            type: "i64",
          },
        ],
      },
    },
    {
      name: "InitializeStakingPoolArgs",
      type: {
        kind: "struct",
        fields: [
          {
            name: "reward_rate",
            type: "u64",
          },
          {
            name: "start_time",
            type: "i64",
          },
          {
            name: "end_time",
            type: "i64",
          },
          {
            name: "unlock_duration",
            type: "i64",
          },
        ],
      },
    },
  ],
};
async function getClaimStatusPda(pool, claimant) {
  return PublicKey.findProgramAddressSync(
    [Buffer.from("claim_status"), pool.toBuffer(), claimant.toBuffer()],
    PROGRAM_ID
  );
}

async function getClaimantAta(claimant, mint) {
  return PublicKey.findProgramAddressSync(
    [
      claimant.toBuffer(),
      Buffer.from([
        6, 221, 246, 225, 215, 101, 161, 147, 217, 203, 225, 70, 206, 235, 121,
        172, 28, 180, 133, 237, 95, 91, 55, 145, 58, 140, 245, 133, 126, 255, 0,
        169,
      ]),
      mint.toBuffer(),
    ],
    new PublicKey([
      140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131,
      11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89,
    ])
  );
}

// Create a simplified IDL without problematic PDA definitions
const SIMPLIFIED_IDL = {
  version: "0.1.0",
  name: "token_staking_program",
  instructions: [
    {
      name: "claim_with_proof",
      discriminator: [38, 165, 237, 119, 50, 165, 25, 163],
      accounts: [
        {
          name: "claimant",
          writable: true,
          signer: true,
        },
        {
          name: "pool",
          writable: true,
        },
        {
          name: "claim_status",
          writable: true,
        },
        {
          name: "vault",
          writable: true,
        },
        {
          name: "claimant_ata",
          writable: true,
        },
        {
          name: "mint",
        },
        {
          name: "token_program",
          address: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        },
        {
          name: "associated_token_program",
          address: "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
        },
        {
          name: "system_program",
          address: "*****************111111111111111",
        },
      ],
      args: [
        {
          name: "allocation",
          type: "u64",
        },
        {
          name: "proof",
          type: {
            vec: {
              array: ["u8", 32],
            },
          },
        },
      ],
    },
  ],
};

async function claimWithProof(
  claimant,
  pool,
  vault,
  mint,
  allocation,
  proof,
  provider
) {
  try {
    const [claimStatus] = await getClaimStatusPda(pool, claimant);
    const [claimantAta] = await getClaimantAta(claimant, mint);

    // Use the simplified IDL to avoid PDA resolution issues
    const program = new anchor.Program(SIMPLIFIED_IDL, PROGRAM_ID, provider);

    console.log("Creating program instance successful");
    console.log("Claimant:", claimant.toString());
    console.log("Pool:", pool.toString());
    console.log("Vault:", vault.toString());
    console.log("Mint:", mint.toString());
    console.log("ClaimStatus PDA:", claimStatus.toString());
    console.log("Claimant ATA:", claimantAta.toString());
    console.log("Allocation:", allocation);
    console.log("Proof length:", proof.length);

    // Check if claim_status account exists
    const claimStatusExists = await checkAccountExists(
      claimStatus,
      "ClaimStatus"
    );
    const claimantAtaExists = await checkAccountExists(
      claimantAta,
      "ClaimantATA"
    );

    // If claim_status account already exists, check if user has already claimed
    if (claimStatusExists) {
      console.log("⚠️  Claim status account already exists!");
      console.log("This might mean the user has already claimed their tokens.");
      console.log("Let's check the account data...");

      try {
        const claimStatusAccountInfo = await connection.getAccountInfo(
          claimStatus
        );
        if (claimStatusAccountInfo && claimStatusAccountInfo.data.length > 0) {
          console.log(
            "Claim status account data length:",
            claimStatusAccountInfo.data.length
          );
          console.log(
            "Account data (first 32 bytes):",
            claimStatusAccountInfo.data.slice(0, 32)
          );

          // Try to decode the account data to see if it shows claimed status
          // The account structure might be: [claimed: bool, amount: u64, ...]
          const claimed = claimStatusAccountInfo.data[0] === 1;
          console.log(
            "Claimed status:",
            claimed ? "✅ ALREADY CLAIMED" : "❌ NOT CLAIMED YET"
          );

          if (claimed) {
            console.log("🚫 User has already claimed their tokens!");
            console.log("Cannot claim again.");
            return null;
          } else {
            console.log(
              "🤔 Account exists but shows not claimed. This might be a different issue."
            );
          }
        }
      } catch (error) {
        console.log(
          "Could not decode claim status account data:",
          error.message
        );
      }
    }

    // Prepare pre-instructions to create missing accounts
    const preInstructions = [
      // Add compute budget instruction to increase compute units
      anchor.web3.ComputeBudgetProgram.setComputeUnitLimit({
        units: 300_000,
      }),
    ];

    // Create ATA if it doesn't exist
    if (!claimantAtaExists) {
      console.log("Creating claimant ATA...");
      try {
        await getOrCreateAssociatedTokenAccount(
          connection,
          provider.wallet.payer,
          mint,
          claimant
        );
        console.log("✅ ATA created successfully");
      } catch (error) {
        console.log("ATA creation will be handled by the program");
      }
    }

    // Convert allocation to BN and format proof properly
    const allocationBN = new anchor.BN(allocation);

    // Convert proof from Buffer objects to Uint8Array format
    const formattedProof = proof.map((buffer) => {
      if (buffer.type === "Buffer" && Array.isArray(buffer.data)) {
        return new Uint8Array(buffer.data);
      } else if (Array.isArray(buffer)) {
        return new Uint8Array(buffer);
      } else {
        return buffer;
      }
    });

    console.log("About to call claimWithProof method...");
    console.log("Formatted allocation:", allocationBN.toString());
    console.log("Formatted proof sample:", formattedProof[0]);

    // Let's try to understand why the claim_status account is causing issues
    console.log("\n=== Debugging claim_status account ===");
    console.log("Claim status PDA seeds:");
    console.log("- pool:", pool.toString());
    console.log("- claimant:", claimant.toString());

    // Check if the PDA derivation is correct
    const [derivedClaimStatus, bump] = PublicKey.findProgramAddressSync(
      [Buffer.from("claim_status"), pool.toBuffer(), claimant.toBuffer()],
      PROGRAM_ID
    );
    console.log("Derived claim_status:", derivedClaimStatus.toString());
    console.log("Expected claim_status:", claimStatus.toString());
    console.log("PDA bump:", bump);
    console.log("PDAs match:", derivedClaimStatus.equals(claimStatus));

    // Let's verify if the proof is actually for this user
    console.log("\n=== Proof Verification Debug ===");
    console.log("User address (claimant):", claimant.toString());
    console.log("Allocation being claimed:", allocation);
    console.log("Proof elements:", formattedProof.length);

    // The PrivilegeEscalation error suggests the proof verification is failing
    // This could mean:
    // 1. The proof is for a different user
    // 2. The allocation amount is wrong
    // 3. The user has already claimed (but account doesn't exist, so unlikely)

    console.log("⚠️  IMPORTANT: The PrivilegeEscalation error suggests:");
    console.log("   - The merkle proof verification is failing");
    console.log(
      "   - The proof in proofTest.json might be for a different user"
    );
    console.log(
      "   - Or the allocation amount (************) might be incorrect"
    );
    console.log(
      "   - Please verify that the proof is actually for user:",
      claimant.toString()
    );

    // Log all account addresses for debugging
    console.log("\n=== Account Addresses ===");
    console.log("Claimant:", claimant.toString());
    console.log("Pool:", pool.toString());
    console.log("Claim Status:", claimStatus.toString());
    console.log("Vault:", vault.toString());
    console.log("Claimant ATA:", claimantAta.toString());
    console.log("Mint:", mint.toString());

    // First, let's create the instruction to inspect it
    const instruction = await program.methods
      .claimWithProof(allocationBN, formattedProof)
      .accounts({
        claimant,
        pool,
        claim_status: claimStatus,
        vault,
        claimant_ata: claimantAta,
        mint,
        token_program: new PublicKey(
          "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
        ),
        associated_token_program: new PublicKey(
          "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
        ),
        system_program: new PublicKey("*****************111111111111111"),
      })
      .instruction();

    console.log("\n=== Transaction Instruction ===");
    console.log("Keys in instruction:");
    instruction.keys.forEach((key, index) => {
      console.log(
        `${index}: ${key.pubkey.toString()} - writable: ${
          key.isWritable
        }, signer: ${key.isSigner}`
      );
    });

    // Try building the transaction manually with explicit account metas
    try {
      console.log("Attempting manual transaction construction...");

      const instruction = await program.methods
        .claimWithProof(allocationBN, formattedProof)
        .accounts({
          claimant,
          pool,
          claim_status: claimStatus,
          vault,
          claimant_ata: claimantAta,
          mint,
          token_program: new PublicKey(
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
          ),
          associated_token_program: new PublicKey(
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
          ),
          system_program: new PublicKey("*****************111111111111111"),
        })
        .instruction();

      // Create transaction manually
      const transaction = new anchor.web3.Transaction();

      // Add compute budget
      transaction.add(
        anchor.web3.ComputeBudgetProgram.setComputeUnitLimit({
          units: 300_000,
        })
      );

      transaction.add(instruction);

      // Send transaction with skipPreflight to bypass simulation
      try {
        const txSig = await provider.sendAndConfirm(transaction, [], {
          skipPreflight: true,
          commitment: "confirmed",
        });
        return txSig;
      } catch (sendError) {
        console.log("Send error:", sendError.message);

        // Try just sending without confirmation
        const signature = await connection.sendRawTransaction(
          transaction.serialize(),
          {
            skipPreflight: true,
          }
        );

        console.log("Transaction sent with signature:", signature);

        // Wait a bit and check if it was successful
        await new Promise((resolve) => setTimeout(resolve, 3000));

        try {
          const txResult = await connection.getTransaction(signature, {
            commitment: "confirmed",
          });

          if (txResult && !txResult.meta?.err) {
            console.log("✅ Transaction confirmed successfully!");
            return signature;
          } else {
            console.log("❌ Transaction failed:", txResult?.meta?.err);
            throw new Error("Transaction failed");
          }
        } catch (confirmError) {
          console.log(
            "Could not confirm transaction, but it was sent:",
            signature
          );
          return signature;
        }
      }
    } catch (manualError) {
      console.log("Manual transaction failed, trying original method...");
      console.log("Manual error:", manualError.message || manualError);

      // Fallback to original method
      const txSig = await program.methods
        .claimWithProof(allocationBN, formattedProof)
        .accounts({
          claimant,
          pool,
          claim_status: claimStatus,
          vault,
          claimant_ata: claimantAta,
          mint,
          token_program: new PublicKey(
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
          ),
          associated_token_program: new PublicKey(
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
          ),
          system_program: new PublicKey("*****************111111111111111"),
        })
        .preInstructions(preInstructions)
        .rpc({ skipPreflight: true, commitment: "confirmed" });

      return txSig;
    }
  } catch (error) {
    throw error;
  }
}

// Function to check if an account exists and is initialized
async function checkAccountExists(publicKey, accountType = "unknown") {
  try {
    const accountInfo = await connection.getAccountInfo(publicKey);
    if (accountInfo) {
      console.log(`✅ ${accountType} account exists:`, publicKey.toString());
      console.log(`   Owner: ${accountInfo.owner.toString()}`);
      console.log(`   Data length: ${accountInfo.data.length} bytes`);
      console.log(`   Lamports: ${accountInfo.lamports}`);

      // If it's a token account, try to get more details
      if (
        accountInfo.owner.toString() ===
          "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" &&
        accountInfo.data.length === 165
      ) {
        try {
          const tokenAccount = await getAccount(connection, publicKey);
          console.log(`   Mint: ${tokenAccount.mint.toString()}`);
          console.log(`   Amount: ${tokenAccount.amount.toString()}`);
          console.log(
            `   Authority: ${tokenAccount.owner?.toString() || "None"}`
          );
        } catch (err) {
          console.log(`   Could not parse token account details`);
        }
      }

      return true;
    } else {
      console.log(
        `❌ ${accountType} account does not exist:`,
        publicKey.toString()
      );
      return false;
    }
  } catch (error) {
    console.error(`Error checking ${accountType} account:`, error.message);
    return false;
  }
}

// Function to find the correct pool PDA
async function findPoolPda(mint, authority) {
  try {
    const [poolPda] = PublicKey.findProgramAddressSync(
      [Buffer.from("pool"), mint.toBuffer(), authority.toBuffer()],
      PROGRAM_ID
    );
    console.log("Calculated Pool PDA:", poolPda.toString());
    return poolPda;
  } catch (error) {
    console.error("Error calculating pool PDA:", error);
    return null;
  }
}

// Function to reverse-engineer pool from vault
async function findPoolFromVault(vaultAddress) {
  try {
    // Since vault PDA = ["vault", pool], we need to find which pool generates this vault
    // We'll try different potential authorities
    const mintAddress = new PublicKey(
      "LEAFqNixpTuk8UCfrxvs2r3MQRB5xEh6Gw9YjMTt6WB"
    );

    // Common authority addresses to try
    const potentialAuthorities = [
      "DkjL9fNcNXWfu1G1xDsctrn6WXXb3epyphKpyQurAhaU", // From batch-transfer.js
      "*****************111111111111111", // System program
      "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", // Token program
    ];

    for (const authorityStr of potentialAuthorities) {
      try {
        const authority = new PublicKey(authorityStr);
        const [poolPda] = PublicKey.findProgramAddressSync(
          [Buffer.from("pool"), mintAddress.toBuffer(), authority.toBuffer()],
          PROGRAM_ID
        );

        const [calculatedVault] = PublicKey.findProgramAddressSync(
          [Buffer.from("vault"), poolPda.toBuffer()],
          PROGRAM_ID
        );

        if (calculatedVault.equals(vaultAddress)) {
          console.log("✅ Found matching pool!");
          console.log("Authority:", authority.toString());
          console.log("Pool:", poolPda.toString());
          console.log("Vault:", calculatedVault.toString());

          // Check if this pool exists
          const poolExists = await checkAccountExists(
            poolPda,
            "Calculated Pool"
          );
          return { pool: poolPda, authority, exists: poolExists };
        }
      } catch (err) {
        // Continue to next authority
      }
    }

    console.log("❌ Could not find pool that generates the given vault");
    return null;
  } catch (error) {
    console.error("Error reverse-engineering pool from vault:", error);
    return null;
  }
}

const main = async () => {
  let totalLeaf = 0;
  let privateKey =
    "3wetLTRayvNqo6poYaq2u7wi5yxQUYpVQvfrTxZfTk8SWiet8nDS6DSDvvS9FebLaVBL8B2ygkRVsxyJUaVHjJyc";

  const keypair = Keypair.fromSecretKey(bs58.decode(privateKey));
  const address = keypair.publicKey;
  const proof = JSON.parse(fs.readFileSync("./proofTest.json", "utf8"));

  console.log("Loaded proof from file:", proof.length, "items");
  console.log("Sample proof item:", proof[0]);
  console.log("User address:", address.toString());

  // Define the accounts
  let poolAddress = new PublicKey(
    "AVx3Y1jkoBMN7bKYkhqQY1kaj41ygJFVf57aVd2Md7Xz"
  );
  const vaultAddress = new PublicKey(
    "8SPNm74MALgCGwDH29x613cZ5bGw6LdP4fcL9pYLSsEg"
  );
  const mintAddress = new PublicKey(
    "LEAFqNixpTuk8UCfrxvs2r3MQRB5xEh6Gw9YjMTt6WB"
  );

  // Check if all accounts exist
  console.log("\n=== Account Verification ===");
  const poolExists = await checkAccountExists(poolAddress, "Pool");
  const vaultExists = await checkAccountExists(vaultAddress, "Vault");
  const mintExists = await checkAccountExists(mintAddress, "Mint");

  // Try to find the correct pool PDA
  console.log("\n=== Pool PDA Calculation ===");

  if (!poolExists) {
    console.log(
      "❌ Pool account doesn't exist. Trying to find correct pool..."
    );

    // Try to reverse-engineer the pool from the vault
    console.log("Attempting to reverse-engineer pool from vault...");
    const poolInfo = await findPoolFromVault(vaultAddress);

    if (poolInfo && poolInfo.exists) {
      console.log("✅ Found correct pool! Updating pool address...");
      // Update the pool address to use the correct one
      poolAddress = poolInfo.pool;
    } else if (poolInfo && !poolInfo.exists) {
      console.log("❌ Found pool PDA but it doesn't exist on-chain");
      console.log("This suggests the pool hasn't been initialized yet");
      return;
    } else {
      console.log("❌ Could not determine correct pool address");
      console.log("Possible solutions:");
      console.log(
        "1. Check if you're on the correct network (mainnet vs devnet)"
      );
      console.log("2. Verify the vault and mint addresses are correct");
      console.log("3. The pool might not be initialized yet");
      return;
    }
  }

  if (!vaultExists || !mintExists) {
    console.log("❌ Some required accounts don't exist. Cannot proceed.");
    return;
  }

  const provider = new anchor.AnchorProvider(
    connection,
    new anchor.Wallet(keypair),
    { preflightCommitment: "confirmed" }
  );

  console.log("\n=== Starting claimWithProof transaction ===");
  try {
    await claimWithProof(
      address,
      poolAddress,
      vaultAddress,
      mintAddress,
      150e9,
      proof,
      provider
    );
  } catch (error) {
    console.error("Transaction failed:", error.message);
    if (error.logs) {
      console.log("Transaction logs:", error.logs);
    }
  }

  // for (let j = 0; j < 1; j++) {
  //   let privateKey = accounts[j].split("|")[1];
  //   let backWoodApi = new BackWoodApi({
  //     baseUrl: "https://launcher.backwoods.gg/v1",
  //   });
  //   await backWoodApi.authenticate(privateKey);
  //   let response = await backWoodApi.proof();
  //   console.log(response, "response");
  //   let data = response.data;
  //   let userLeaf = 0;
  //   if (data) {
  //     for (let i = 0; i < data.length; i++) {
  //       totalLeaf += data[i].allocation;
  //       userLeaf += data[i].allocation;
  //     }
  //     console.log(
  //       `Account ${j + 1}: ${
  //         accounts[j].split("|")[0]
  //       } - Claimed ${userLeaf} LEAF`
  //     );
  //   } else {
  //     console.log(response);
  //   }
  // }

  console.log(`Total LEAF Claimed: ${totalLeaf}`);
};

main();
